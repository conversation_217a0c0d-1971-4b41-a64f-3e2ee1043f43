<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Establishment;

class EstablishmentPolicy
{
    public function viewAny(Admin $admin): bool
    {
        return $admin->hasPermissionTo('view_establishments') || $admin->hasPermissionTo('manage_establishments');
    }

    public function view(Admin $admin, Establishment $establishment): bool
    {
        return $admin->hasPermissionTo('view_establishments');
    }

    public function create(Admin $admin): bool
    {
        return $admin->hasPermissionTo('create_establishments');
    }

    public function update(Admin $admin, Establishment $establishment): bool
    {
        return $admin->hasPermissionTo('edit_establishments');
    }

    public function delete(Admin $admin, Establishment $establishment): bool
    {
        return $admin->hasPermissionTo('delete_establishments');
    }
}