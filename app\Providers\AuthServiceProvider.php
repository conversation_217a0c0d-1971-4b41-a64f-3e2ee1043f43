<?php

namespace App\Providers;

use App\Models\Admin;
use App\Models\Agency;
use App\Models\Client;
use App\Models\Degree;
use App\Models\Delegation;
use App\Models\Establishment;
use App\Models\Governorate;
use App\Models\GovernoratePurchaseOrder;
use App\Models\Line;
use App\Models\LocationSeason;
use App\Models\LocationType;
use App\Models\PaymentMethod;
use App\Models\TypeEstablishment;
use App\Models\TypeVehicule;
use App\Models\TypeVehicleTypeLocation;
use App\Models\TypeVehiculeSaisonLocation;
use App\Models\CardType;
use App\Models\SubsType;
use App\Models\TariffBase;
use App\Models\Season;
use App\Models\Trip;
use App\Models\TariffOption;
use App\Models\SalePeriod;
use App\Models\SalePoint;
use App\Models\Subscription;
use App\Models\Periodicity;
use App\Policies\AdminPolicy;
use App\Policies\AgencyPolicy;
use App\Policies\ClientPolicy;
use App\Policies\DegreePolicy;
use App\Policies\DelegationPolicy;
use App\Policies\EstablishmentPolicy;
use App\Policies\GovernoratePolicy;
use App\Policies\GovernoratePurchaseOrderPolicy;
use App\Policies\LocationSeasonPolicy;
use App\Policies\LocationTypePolicy;
use App\Policies\MotifDuplicatePolicy;
use App\Policies\PaymentMethodPolicy;
use App\Policies\TypeEstablishmentPolicy;
use App\Policies\TypeVehiculePolicy;
use App\Policies\TypeVehicleTypeLocationPolicy;
use App\Policies\TypeVehiculeSaisonLocationPolicy;
use App\Policies\CardTypePolicy;
use App\Policies\SubsTypePolicy;
use App\Policies\TariffBasePolicy;
use App\Policies\LinePolicy;
use App\Policies\SeasonPolicy;
use App\Policies\TripPolicy;
use App\Policies\TariffOptionPolicy;
use App\Policies\SalePeriodPolicy;
use App\Policies\SalePointPolicy;
use App\Policies\SubscriptionPolicy;
use App\Policies\PeriodicityPolicy;
use App\Policies\DiscountPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Line::class => LinePolicy::class,
        Governorate::class => GovernoratePolicy::class,
        Delegation::class => DelegationPolicy::class,
        Agency::class => AgencyPolicy::class,
        Establishment::class => EstablishmentPolicy::class,
        Client::class => ClientPolicy::class,
        GovernoratePurchaseOrder::class => GovernoratePurchaseOrderPolicy::class,
        Admin::class => AdminPolicy::class,
        MotifDuplicate::class => MotifDuplicatePolicy::class,
        TypeEstablishment::class => TypeEstablishmentPolicy::class,
        Degree::class => DegreePolicy::class,
        PaymentMethod::class => PaymentMethodPolicy::class,
        CardType::class => CardTypePolicy::class,
        SubsType::class => SubsTypePolicy::class,
        TariffBase::class => TariffBasePolicy::class,
        Season::class => SeasonPolicy::class,
        Trip::class => TripPolicy::class,
        TariffOption::class => TariffOptionPolicy::class,
        Campaign::class => CampaignPolicy::class,
        SalePeriod::class => SalePeriodPolicy::class,
        SalePoint::class => SalePointPolicy::class,
        AffectationAgent::class => AffectationAgentPolicy::class,
        TypeClient::class => TypeClientPolicy::class,
        Periodicity::class => PeriodicityPolicy::class,
        Discount::class => DiscountPolicy::class,
        WebsiteTrip::class => WebsiteTripPolicy::class,
        CardFee::class => CardFeePolicy::class,
        LocationType::class => LocationTypePolicy::class,
        LocationSeason::class => LocationSeasonPolicy::class,
        TypeVehicule::class => TypeVehiculePolicy::class,
        TypeVehicleTypeLocation::class => TypeVehicleTypeLocationPolicy::class,
        TypeVehiculeSaisonLocation::class => TypeVehiculeSaisonLocationPolicy::class,
        Station::class => StationPolicy::class,
        SubsCard::class => SubsCardPolicy::class,
        Subscription::class => SubscriptionPolicy::class,
    ];
    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        Gate::before(function ($user, $ability) {
            if ($user->hasRole('SUPER_ADMIN')) {
                // Define abilities that should still be checked even for SUPER_ADMIN
                $restrictedAbilities = ['delete']; // Add more abilities as needed

                // If this is a restricted ability, let the policy handle it
                if (in_array($ability, $restrictedAbilities)) {
                    return null; // Let the policy decide
                }

                // For all other abilities, grant access
                return true;
            }
        });
        $this->registerPolicies();
    }
}

















