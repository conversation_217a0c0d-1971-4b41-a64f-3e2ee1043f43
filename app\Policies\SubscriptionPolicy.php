<?php

namespace App\Policies;

use App\Models\Admin;
use App\Models\Subscription;
use Illuminate\Auth\Access\HandlesAuthorization;

class SubscriptionPolicy
{
    use HandlesAuthorization;    
    public function viewAny(Admin $user): bool
    {
        return $user->hasPermissionTo('view_newSubs') || $user->hasPermissionTo('manage_newSubs');
    }

    public function view(Admin $user, Subscription $subscription): bool
    {
        return $user->hasPermissionTo('view_newSubs');
    }

    public function create(Admin $user): bool
    {
        return $user->hasPermissionTo('create_newSubs');
    }

    public function update(Admin $user, Subscription $subscription): bool
    {
        return $user->hasPermissionTo('edit_newSubs');
    }

    public function delete(Admin $user, Subscription $subscription): bool
    {
        return $user->hasPermissionTo('delete_newSubs');
    }
}
